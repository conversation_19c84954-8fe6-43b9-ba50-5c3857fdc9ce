.avatar {
  border-radius: 9999px;
  overflow: hidden;
  position: relative;
}

.avatarSm {
  width: 2rem;
  height: 2rem;
}

.avatarMd {
  width: 2.5rem;
  height: 2.5rem;
}

.avatarLg {
  width: 3rem;
  height: 3rem;
}

.fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color, #6b21a8);
  color: var(--primary-content, #ffffff);
}

.fallbackText {
  font-weight: 700;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.loading {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(249, 250, 251, 0.5);
  z-index: 10;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.googleAvatar {
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}
